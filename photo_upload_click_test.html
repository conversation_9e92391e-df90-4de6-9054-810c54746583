<!DOCTYPE html>
<html>
<head>
    <title>Photo Upload Click Test</title>
    <style>
        .test-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
            cursor: pointer;
            background-color: #f8f9fa;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            border: 2px dashed #007bff;
            transition: all 0.3s ease;
            text-align: center;
            width: 100%;
            margin: 10px 0;
        }

        .file-input-wrapper:hover {
            background-color: #007bff;
            color: white;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            left: -9999px;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Photo Upload Click Functionality Test</h1>
        
        <div class="test-result info">
            <strong>Instructions:</strong>
            <ol>
                <li>Click on the blue dashed area below</li>
                <li>It should open your file picker dialog</li>
                <li>Select any image file</li>
                <li>The text should change to show the selected file name</li>
            </ol>
        </div>
        
        <h3>Test File Input (Same as Recognition Page):</h3>
        
        <div class="file-input-wrapper" id="testWrapper">
            <input type="file" id="testFileInput" accept="image/*">
            <i class="fas fa-upload"></i>
            Select photo for recognition
        </div>
        
        <div id="testResults" class="test-result" style="display: none;">
            <strong>Test Results:</strong>
            <div id="resultDetails"></div>
        </div>
        
        <h3>How This Works:</h3>
        <ul>
            <li><strong>CSS:</strong> The actual file input is hidden using <code>position: absolute; left: -9999px;</code></li>
            <li><strong>JavaScript:</strong> Click event on wrapper triggers the hidden file input</li>
            <li><strong>Visual Feedback:</strong> File name appears when selected, styling changes</li>
        </ul>
        
        <div class="test-result info">
            <strong>Expected Behavior:</strong><br>
            ✅ Clicking the dashed area opens file picker<br>
            ✅ Selecting a file shows the file name<br>
            ✅ Border color and background change to indicate success<br>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const wrapper = document.getElementById('testWrapper');
            const fileInput = document.getElementById('testFileInput');
            const resultsDiv = document.getElementById('testResults');
            const resultDetails = document.getElementById('resultDetails');
            
            // Add click handler for file upload area
            wrapper.addEventListener('click', function() {
                console.log('Wrapper clicked - triggering file input');
                fileInput.click();
            });
            
            // Handle file selection
            fileInput.addEventListener('change', function() {
                const fileName = this.files[0] ? this.files[0].name : 'Select photo for recognition';
                const iconElement = wrapper.querySelector('i');
                const textNodes = [];
                
                // Find text nodes
                for (let node of wrapper.childNodes) {
                    if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
                        textNodes.push(node);
                    }
                }
                
                // Update text
                if (textNodes.length > 0) {
                    textNodes[textNodes.length - 1].textContent = fileName;
                }
                
                // Update visual feedback
                if (this.files[0]) {
                    wrapper.style.borderColor = '#28a745';
                    wrapper.style.backgroundColor = '#d4edda';
                    if (iconElement) {
                        iconElement.className = 'fas fa-check-circle';
                    }
                    
                    // Show results
                    resultsDiv.style.display = 'block';
                    resultsDiv.className = 'test-result success';
                    resultDetails.innerHTML = `
                        ✅ Click handler working<br>
                        ✅ File selected: ${fileName}<br>
                        ✅ File size: ${(this.files[0].size / 1024).toFixed(1)} KB<br>
                        ✅ File type: ${this.files[0].type}<br>
                        ✅ Visual feedback working
                    `;
                }
            });
        });
    </script>
</body>
</html>
