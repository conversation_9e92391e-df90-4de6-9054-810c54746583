<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Face Recognition Academic System{% endblock %}</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if current_user.is_authenticated %}
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-user-graduate"></i>
                Face Recognition System
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="{{ url_for('dashboard') }}"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="{{ url_for('students') }}"><i class="fas fa-users"></i> Students</a></li>
                    <li><a href="{{ url_for('attendance') }}"><i class="fas fa-calendar-check"></i> Attendance</a></li>
                    <li><a href="{{ url_for('recognition') }}"><i class="fas fa-camera"></i> Recognition</a></li>
                    <li><a href="{{ url_for('realtime_recognition') }}"><i class="fas fa-video"></i> Live Recognition</a></li>
                    <li><a href="{{ url_for('reports') }}"><i class="fas fa-chart-bar"></i> Reports</a></li>
                </ul>
            </nav>
            <form method="POST" action="{{ url_for('logout') }}" style="margin: 0;">
                <button type="submit" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </form>
        </div>
    </header>
    {% endif %}

    <main>
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="container">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'error' if category == 'error' else category }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
