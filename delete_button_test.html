<!DOCTYPE html>
<html>
<head>
    <title>Delete Button Click Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        
        .btn {
            display: inline-block;
            padding: 0.375rem 0.75rem;
            margin: 0.25rem;
            border: 1px solid;
            border-radius: 0.25rem;
            text-decoration: none;
            cursor: pointer;
        }
        
        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .test-results {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>🧪 Delete Button Click Test</h1>
    
    <p>This tests the same HTML structure as the students page delete buttons:</p>
    
    <h3>Test Buttons:</h3>
    
    <!-- Test button 1: Click on the link itself -->
    <a href="#test1" 
       class="btn btn-danger btn-sm"
       data-confirm="Are you sure you want to delete Test Student 1? This action cannot be undone.">
        <i class="fas fa-trash"></i> Link Click Test
    </a>
    
    <!-- Test button 2: Click on icon inside (the problematic case) -->
    <a href="#test2" 
       class="btn btn-danger btn-sm"
       data-confirm="Are you sure you want to delete Test Student 2? This action cannot be undone.">
        <i class="fas fa-trash"></i>
    </a>
    
    <div class="test-results">
        <strong>Test Instructions:</strong>
        <ol>
            <li>Click the first button (should work - clicking on text area)</li>
            <li>Click the second button icon (this is what was failing before)</li>
            <li>Both should show confirmation dialog with student-specific message</li>
            <li>If you click "OK", it should navigate (or show test message)</li>
        </ol>
    </div>
    
    <div id="results" style="margin-top: 20px; padding: 10px; background: #d4edda; border-radius: 4px; display: none;">
        <strong>Test Results:</strong>
        <div id="resultsContent"></div>
    </div>

    <script>
        // Same logic as the fixed confirmDelete function
        function confirmDelete(event) {
            event.preventDefault();
            
            // Find the actual link element (could be event.target or its parent)
            let linkElement = event.target;
            
            // If we clicked on an icon inside the link, get the parent link
            if (linkElement.tagName === 'I') {
                linkElement = linkElement.parentElement;
            }
            
            // Get the confirmation message and href from the link element
            const message = linkElement.dataset.confirm || 'Are you sure you want to delete this item?';
            const href = linkElement.href;
            
            // Show test results instead of actually navigating
            const resultsDiv = document.getElementById('results');
            const resultsContent = document.getElementById('resultsContent');
            
            resultsContent.innerHTML = `
                <p>✅ Click handler working correctly!</p>
                <p><strong>Target element:</strong> ${event.target.tagName} (${event.target.className || 'no class'})</p>
                <p><strong>Link element:</strong> ${linkElement.tagName} (${linkElement.className})</p>
                <p><strong>Message:</strong> ${message}</p>
                <p><strong>URL:</strong> ${href}</p>
                <p><strong>Confirmation result:</strong> ${confirm(message) ? 'User clicked OK - would navigate' : 'User clicked Cancel - no action'}</p>
            `;
            
            resultsDiv.style.display = 'block';
        }

        // Add event listeners to delete buttons
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('.btn-danger[data-confirm]');
            deleteButtons.forEach(btn => {
                btn.addEventListener('click', confirmDelete);
            });
            
            console.log(`Added event listeners to ${deleteButtons.length} delete buttons`);
        });
    </script>
</body>
</html>
