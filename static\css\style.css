/* Face Recognition Academic System - Modern UI */

:root {
    --primary-color: #3498db;
    --accent-color: #d6eaf8;
    --background-color: #f5f5f5;
    --text-color: #333333;
    --success-color: #2ecc71;
    --error-color: #e74c3c;
    --warning-color: #f39c12;
    --white: #ffffff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.2);
    --border-radius: 8px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #2980b9 100%);
    color: var(--white);
    padding: 1rem 0;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo i {
    font-size: 2rem;
}

.nav-links {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.nav-links a {
    color: var(--white);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-links a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.logout-btn {
    background-color: var(--error-color);
    border: none;
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.logout-btn:hover {
    background-color: #c0392b;
}

/* Main Container */
.container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--accent-color);
}

.card-title {
    font-size: 1.5rem;
    color: var(--primary-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: #2980b9;
    transform: translateY(-1px);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
}

.btn-success:hover {
    background-color: #27ae60;
    transform: translateY(-1px);
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--white);
}

.btn-warning:hover {
    background-color: #e67e22;
    transform: translateY(-1px);
}

.btn-danger {
    background-color: var(--error-color);
    color: var(--white);
}

.btn-danger:hover {
    background-color: #c0392b;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #95a5a6;
    color: var(--white);
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    transform: translateY(-1px);
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.file-input-wrapper {
    position: relative;
    overflow: hidden;
    display: inline-block;
    cursor: pointer;
    background-color: var(--accent-color);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    border: 2px dashed var(--primary-color);
    transition: var(--transition);
    text-align: center;
    width: 100%;
}

.file-input-wrapper:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.file-input-wrapper input[type=file] {
    position: absolute;
    left: -9999px;
}

/* Grid Layout */
.grid {
    display: grid;
    gap: 2rem;
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Tables */
.table-container {
    overflow-x: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.table th {
    background-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
}

.table tr:hover {
    background-color: var(--accent-color);
}

/* Status Badges */
.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.badge-success {
    background-color: var(--success-color);
    color: var(--white);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--white);
}

.badge-error {
    background-color: var(--error-color);
    color: var(--white);
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-success {
    background-color: #d4edda;
    border-color: var(--success-color);
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border-color: var(--error-color);
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: var(--warning-color);
    color: #856404;
}

.alert-info {
    background-color: var(--accent-color);
    border-color: var(--primary-color);
    color: #004085;
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Camera Preview */
.camera-container {
    position: relative;
    max-width: 640px;
    margin: 0 auto;
}

.camera-preview {
    width: 100%;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.face-box {
    position: absolute;
    border: 3px solid var(--success-color);
    border-radius: 4px;
    background: rgba(46, 204, 113, 0.1);
}

.face-label {
    position: absolute;
    bottom: -25px;
    left: 0;
    background: var(--success-color);
    color: var(--white);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Recognition Results */
.recognition-results {
    margin-top: 2rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--white);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow);
}

.result-info {
    flex: 1;
}

.result-name {
    font-weight: 600;
    color: var(--text-color);
}

.result-details {
    font-size: 0.875rem;
    color: #666;
    margin-top: 0.25rem;
}

.confidence-bar {
    width: 100px;
    height: 8px;
    background: #eee;
    border-radius: 4px;
    overflow: hidden;
    margin-left: 1rem;
}

.confidence-fill {
    height: 100%;
    background: var(--success-color);
    transition: width 0.3s ease;
}

/* Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    display: block;
}

.stat-label {
    color: #666;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* Login Page */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, #2980b9 100%);
}

.login-card {
    background: var(--white);
    padding: 3rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    width: 100%;
    max-width: 400px;
}

.login-title {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--primary-color);
    font-size: 2rem;
    font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
    }

    .container {
        padding: 0 1rem;
    }

    .card {
        padding: 1rem;
    }

    .card-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .grid-2,
    .grid-3 {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .result-item {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .confidence-bar {
        margin: 0;
        width: 100%;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.d-flex { display: flex; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }
.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

/* Additional utility classes for attendance.html */
.ml-2 { margin-left: 0.5rem; }
.text-center { text-align: center; }
.text-muted { color: #6c757d; }

/* Progress bars and confidence indicators */
.confidence-bar {
    background-color: #e9ecef;
    border-radius: 4px;
    height: 8px;
    position: relative;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.confidence-fill.confidence-success { background-color: var(--success-color); }
.confidence-fill.confidence-warning { background-color: var(--warning-color); }
.confidence-fill.confidence-error { background-color: var(--error-color); }

.progress-bar {
    background-color: #e9ecef;
    border-radius: 5px;
    overflow: hidden;
}
