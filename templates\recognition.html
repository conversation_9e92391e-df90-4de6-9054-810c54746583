{% extends "base.html" %}

{% block title %}Face Recognition - Face Recognition System{% endblock %}

{% block content %}
<div class="container">
    <!-- Real-time Recognition Card -->
    <div class="card">
        <div class="card-header">
            <h1 class="card-title">
                <i class="fas fa-video"></i>
                Real-time Face Recognition
            </h1>
        </div>
        
        <div class="grid grid-2">
            <div>
                <div class="camera-container">
                    <video id="cameraPreview" class="camera-preview" autoplay muted></video>
                    <div class="camera-overlay" id="cameraOverlay"></div>
                </div>
                
                <div class="d-flex gap-2 mt-3 justify-center">
                    <button id="startCamera" class="btn btn-success">
                        <i class="fas fa-play"></i> Start Camera
                    </button>
                    <button id="stopCamera" class="btn btn-danger" style="display: none;">
                        <i class="fas fa-stop"></i> Stop Camera
                    </button>
                    <button id="capturePhoto" class="btn btn-primary" disabled>
                        <i class="fas fa-camera"></i> Capture Photo
                    </button>
                    <button id="toggleRecognition" class="btn btn-success">
                        <i class="fas fa-eye"></i> Start Recognition
                    </button>
                </div>
            </div>
            
            <div>
                <h3>Recognition Results</h3>
                <div id="realTimeResults" class="recognition-results">
                    <p class="text-center text-muted">Start recognition to see results</p>
                </div>
                
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>How to use:</strong>
                    <ul class="mt-2" style="margin-bottom: 0;">
                        <li>Click "Start Camera" to activate webcam</li>
                        <li>Click "Start Recognition" for real-time detection</li>
                        <li>Recognized students will appear on the right</li>
                        <li>Click "Mark Attendance" to record presence</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Photo Upload Recognition -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i class="fas fa-image"></i>
                Photo-based Recognition
            </h2>
        </div>
        
        <form id="photoRecognitionForm" enctype="multipart/form-data">
            <div class="grid grid-2">
                <div>
                    <div class="form-group">
                        <label for="uploadPhoto" class="form-label">
                            <i class="fas fa-upload"></i> Upload Photo
                        </label>
                        <div class="file-input-wrapper">
                            <input type="file" id="uploadPhoto" name="photo" accept="image/*" required>
                            <i class="fas fa-upload"></i>
                            Select photo for recognition
                        </div>
                        <small class="form-text">
                            Upload a photo containing one or more faces to recognize.
                            Supported formats: JPG, PNG. Max size: 16MB.
                        </small>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Recognize Faces
                    </button>
                </div>
                
                <div>
                    <h4>Recognition Results</h4>
                    <div id="recognitionResults" class="recognition-results">
                        <p class="text-center text-muted">Upload a photo to see recognition results</p>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Bulk Attendance -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i class="fas fa-users"></i>
                Bulk Attendance from Group Photo
            </h2>
        </div>
        
        <div class="grid grid-2">
            <div>
                <div class="form-group">
                    <label for="groupPhoto" class="form-label">
                        <i class="fas fa-users"></i> Group Photo
                    </label>
                    <div class="file-input-wrapper">
                        <input type="file" id="groupPhoto" name="group_photo" accept="image/*">
                        <i class="fas fa-users"></i>
                        Upload group photo for attendance
                    </div>
                    <small class="form-text">
                        Upload a group photo to automatically mark attendance for all recognized students.
                        This will process all faces in the image and mark attendance for today.
                    </small>
                </div>
                
                <button type="button" id="bulkMarkBtn" class="btn btn-warning" onclick="bulkMarkAttendance()">
                    <i class="fas fa-calendar-check"></i> Mark Bulk Attendance
                </button>
                
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Note:</strong> This will mark attendance for ALL recognized faces in the photo.
                    Students who already have attendance marked today will be skipped.
                </div>
            </div>
            
            <div>
                <h4>Bulk Processing Results</h4>
                <div id="bulkResults">
                    <p class="text-center text-muted">Upload a group photo and click "Mark Bulk Attendance" to see results</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i class="fas fa-lightbulb"></i>
                Usage Instructions
            </h2>
        </div>
        
        <div class="grid grid-3">
            <div class="text-center">
                <i class="fas fa-video" style="font-size: 3rem; color: var(--success-color); margin-bottom: 1rem;"></i>
                <h4>Real-time Recognition</h4>
                <ul style="text-align: left;">
                    <li>Best for individual identification</li>
                    <li>Real-time feedback</li>
                    <li>Manual attendance marking</li>
                    <li>Live confidence scores</li>
                </ul>
            </div>
            
            <div class="text-center">
                <i class="fas fa-image" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 1rem;"></i>
                <h4>Photo Recognition</h4>
                <ul style="text-align: left;">
                    <li>Upload any photo</li>
                    <li>Identify all faces</li>
                    <li>Review before marking</li>
                    <li>Single or multiple faces</li>
                </ul>
            </div>
            
            <div class="text-center">
                <i class="fas fa-users" style="font-size: 3rem; color: var(--warning-color); margin-bottom: 1rem;"></i>
                <h4>Bulk Attendance</h4>
                <ul style="text-align: left;">
                    <li>Process group photos</li>
                    <li>Automatic attendance</li>
                    <li>Multiple students at once</li>
                    <li>Perfect for classrooms</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// Add click handler for file upload area
document.addEventListener('DOMContentLoaded', function() {
    // Handle file input click for photo recognition
    const uploadPhotoWrapper = document.querySelector('#photoRecognitionForm .file-input-wrapper');
    const uploadPhotoInput = document.getElementById('uploadPhoto');
    
    if (uploadPhotoWrapper && uploadPhotoInput) {
        uploadPhotoWrapper.addEventListener('click', function() {
            uploadPhotoInput.click();
        });
        
        // Update display when file is selected
        uploadPhotoInput.addEventListener('change', function() {
            const fileName = this.files[0] ? this.files[0].name : 'Select photo for recognition';
            const iconElement = uploadPhotoWrapper.querySelector('i');
            const textNode = uploadPhotoWrapper.childNodes[uploadPhotoWrapper.childNodes.length - 1];
            
            if (textNode && textNode.nodeType === Node.TEXT_NODE) {
                textNode.textContent = fileName;
            }
            
            // Change icon to indicate file selected
            if (iconElement && this.files[0]) {
                iconElement.className = 'fas fa-check-circle';
                uploadPhotoWrapper.style.borderColor = '#28a745';
                uploadPhotoWrapper.style.backgroundColor = '#d4edda';
            }
        });
    }
    
    // Handle file input click for bulk attendance
    const groupPhotoWrapper = document.querySelector('#groupPhoto').parentElement;
    const groupPhotoInput = document.getElementById('groupPhoto');
    
    if (groupPhotoWrapper && groupPhotoInput) {
        groupPhotoWrapper.addEventListener('click', function() {
            groupPhotoInput.click();
        });
        
        // Update display when file is selected
        groupPhotoInput.addEventListener('change', function() {
            const fileName = this.files[0] ? this.files[0].name : 'Upload group photo for attendance';
            const iconElement = groupPhotoWrapper.querySelector('i');
            const textNode = groupPhotoWrapper.childNodes[groupPhotoWrapper.childNodes.length - 1];
            
            if (textNode && textNode.nodeType === Node.TEXT_NODE) {
                textNode.textContent = fileName;
            }
            
            // Change icon to indicate file selected
            if (iconElement && this.files[0]) {
                iconElement.className = 'fas fa-check-circle';
                groupPhotoWrapper.style.borderColor = '#ffc107';
                groupPhotoWrapper.style.backgroundColor = '#fff3cd';
            }
        });
    }
});

// Handle photo recognition form
document.getElementById('photoRecognitionForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const fileInput = document.getElementById('uploadPhoto');
    
    if (!fileInput.files[0]) {
        app.showAlert('Please select a photo first', 'error');
        return;
    }
    
    try {
        app.showLoading('photoRecognitionForm');

        const response = await fetch('/recognize_photo', {
            method: 'POST',
            body: formData
        });

        // Check if response is ok
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
            app.displayRecognitionResults(result);

            // Show appropriate success message based on results
            if (result.faces_detected === 0) {
                app.showAlert('No faces detected in the uploaded image. Please try with a clearer image containing visible faces.', 'warning');
            } else if (result.faces_recognized === 0) {
                app.showAlert(`Detected ${result.faces_detected} face(s) but none were recognized. The faces may not be in the student database.`, 'warning');
            } else {
                const message = result.faces_detected === 1
                    ? `Successfully recognized the face in the image!`
                    : `Successfully processed ${result.faces_detected} face(s), recognized ${result.faces_recognized}`;
                app.showAlert(message, 'success');
            }
        } else {
            app.showAlert(result.message || 'Failed to recognize faces', 'error');
        }
    } catch (error) {
        console.error('Error recognizing photo:', error);

        // Provide more specific error messages
        let errorMessage = 'Failed to process photo. ';
        if (error.message.includes('HTTP 413')) {
            errorMessage += 'File too large. Please use a smaller image.';
        } else if (error.message.includes('HTTP 400')) {
            errorMessage += 'Invalid image file. Please check the file format.';
        } else if (error.message.includes('HTTP 500')) {
            errorMessage += 'Server error. Please try again later.';
        } else if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
            errorMessage += 'Network error. Please check your connection and try again.';
        } else {
            errorMessage += 'Please try again or contact support if the problem persists.';
        }

        app.showAlert(errorMessage, 'error');
    } finally {
        app.hideLoading('photoRecognitionForm');
    }
});
</script>

<style>
/* Enhanced Recognition Results */
.enhanced-result-item {
    margin: 1rem 0;
}

.result-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.result-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.best-match .result-card {
    border-color: #28a745;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.result-header {
    background: #f8f9fa;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.best-match-badge {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.student-photo-container {
    display: flex;
    justify-content: center;
}

.student-photo-result {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.result-body {
    padding: 1rem;
}

.student-name {
    margin: 0 0 0.5rem 0;
    color: #333;
    font-weight: 600;
}

.student-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #666;
}

.student-details i {
    margin-right: 0.5rem;
    color: #888;
}

.confidence-section {
    margin-bottom: 1rem;
}

.confidence-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #333;
}

.confidence-display {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.confidence-bar-large {
    flex: 1;
    height: 20px;
    background: #eee;
    border-radius: 10px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.confidence-success { background: #28a745; }
.confidence-warning { background: #ffc107; }
.confidence-error { background: #dc3545; }

.confidence-percentage {
    font-weight: 600;
    font-size: 1rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-buttons .btn {
    flex: 1;
    max-width: 150px;
}

/* Modal styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-dialog {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h4 {
    margin: 0;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 1rem;
}

.modal-footer {
    padding: 1rem;
    border-top: 1px solid #eee;
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.captured-face-preview {
    width: 100%;
    max-width: 200px;
    height: auto;
    border-radius: 8px;
    border: 2px solid #ddd;
    display: block;
    margin: 1rem auto;
}

.no-matches-container,
.add-new-container {
    text-align: center;
    margin: 1rem 0;
}

.results-header {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.results-header h4 {
    margin: 0;
    color: #333;
}

.results-summary {
    margin-top: 0.5rem;
}

.summary-stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.stat-item {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #666;
}

.stat-item i {
    color: #888;
}

.no-faces-container,
.no-matches-container,
.unrecognized-faces-container,
.add-new-container,
.reupload-container {
    margin: 1rem 0;
}

.no-faces-container .alert {
    border-left: 4px solid #17a2b8;
}

.no-matches-container .alert {
    border-left: 4px solid #ffc107;
}

.unrecognized-faces-container .alert {
    border-left: 4px solid #17a2b8;
}

.add-new-container .alert {
    border-left: 4px solid #ffc107;
}
</style>
{% endblock %}
